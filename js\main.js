// AI Learning Hub - Main JavaScript

// Experiment data configuration
const experiments = [
    {
        id: 1,
        title: "Simple Text-Based Chatbot",
        description: "Build your first AI using if-else statements to create conversational responses",
        difficulty: "beginner",
        level: "beginner",
        duration: "30 min",
        prerequisites: ["Basic Python syntax", "Conditional statements"],
        nextSteps: ["AI Applications", "Search Algorithms"],
        path: "experiments/01-chatbot/index.html"
    },
    {
        id: 2,
        title: "AI Applications Showcase",
        description: "Explore real-world AI applications in healthcare, transport, and environmental protection",
        difficulty: "beginner",
        level: "beginner",
        duration: "45 min",
        prerequisites: ["Understanding of basic AI concepts"],
        nextSteps: ["Search Algorithms", "Problem Solving"],
        path: "experiments/02-ai-applications/index.html"
    },
    {
        id: 3,
        title: "Greedy Best-First Search",
        description: "Implement heuristic-based search algorithms for efficient pathfinding",
        difficulty: "beginner",
        level: "beginner",
        duration: "60 min",
        prerequisites: ["Data structures", "Graph concepts"],
        nextSteps: ["A* Search", "Advanced Search"],
        path: "experiments/03-greedy-search/index.html"
    },
    {
        id: 4,
        title: "A* Search Algorithm",
        description: "Master optimal pathfinding with the A* algorithm combining cost and heuristics",
        difficulty: "intermediate",
        level: "intermediate",
        duration: "75 min",
        prerequisites: ["Greedy Search", "Graph algorithms"],
        nextSteps: ["Game Theory", "Minimax"],
        path: "experiments/04-a-star/index.html"
    },
    {
        id: 5,
        title: "Tic-Tac-Toe with Minimax",
        description: "Create an unbeatable AI player using minimax algorithm and alpha-beta pruning",
        difficulty: "intermediate",
        level: "intermediate",
        duration: "90 min",
        prerequisites: ["Game theory basics", "Recursion"],
        nextSteps: ["Probability", "Bayes Theorem"],
        path: "experiments/05-minimax/index.html"
    },
    {
        id: 6,
        title: "Bayes' Theorem for Disease Detection",
        description: "Apply probabilistic reasoning for medical diagnosis and decision making",
        difficulty: "intermediate",
        level: "intermediate",
        duration: "60 min",
        prerequisites: ["Basic probability", "Statistics"],
        nextSteps: ["Logic Systems", "Knowledge Representation"],
        path: "experiments/06-bayes/index.html"
    },
    {
        id: 7,
        title: "Predicate Logic Implementation",
        description: "Build logical reasoning systems using predicate logic and truth evaluation",
        difficulty: "advanced",
        level: "advanced",
        duration: "75 min",
        prerequisites: ["Boolean logic", "Set theory"],
        nextSteps: ["First Order Logic", "Knowledge Systems"],
        path: "experiments/07-predicate-logic/index.html"
    },
    {
        id: 8,
        title: "First Order Logic (FOL)",
        description: "Implement advanced logical representation with quantifiers and complex predicates",
        difficulty: "advanced",
        level: "advanced",
        duration: "90 min",
        prerequisites: ["Predicate Logic", "Mathematical logic"],
        nextSteps: ["Inference Systems", "Rule-based Systems"],
        path: "experiments/08-first-order-logic/index.html"
    },
    {
        id: 9,
        title: "Forward and Backward Chaining",
        description: "Build inference engines using forward and backward chaining strategies",
        difficulty: "advanced",
        level: "advanced",
        duration: "105 min",
        prerequisites: ["Logic systems", "Rule representation"],
        nextSteps: ["Machine Learning", "Expert Systems"],
        path: "experiments/09-chaining/index.html"
    },
    {
        id: 10,
        title: "Decision Tree Classifier",
        description: "Implement machine learning classification using the iris dataset",
        difficulty: "advanced",
        level: "advanced",
        duration: "90 min",
        prerequisites: ["Statistics", "Data analysis"],
        nextSteps: ["Expert Systems", "Knowledge Acquisition"],
        path: "experiments/10-decision-tree/index.html"
    },
    {
        id: 11,
        title: "Rule-Based Expert System",
        description: "Create a medical diagnosis expert system with rule-based reasoning",
        difficulty: "advanced",
        level: "advanced",
        duration: "120 min",
        prerequisites: ["Logic systems", "Knowledge representation"],
        nextSteps: ["Knowledge Acquisition", "Advanced AI"],
        path: "experiments/11-expert-system/index.html"
    },
    {
        id: 12,
        title: "Knowledge Acquisition & Rule Generation",
        description: "Automatically generate expert system rules from decision tree analysis",
        difficulty: "advanced",
        level: "advanced",
        duration: "135 min",
        prerequisites: ["Decision Trees", "Expert Systems"],
        nextSteps: ["Advanced AI Topics", "Research Projects"],
        path: "experiments/12-knowledge-acquisition/index.html"
    }
];

// Progress tracking system
class ProgressTracker {
    constructor() {
        this.storageKey = 'ai-learning-progress';
        this.progress = this.loadProgress();
    }

    loadProgress() {
        const saved = localStorage.getItem(this.storageKey);
        return saved ? JSON.parse(saved) : {};
    }

    saveProgress() {
        localStorage.setItem(this.storageKey, JSON.stringify(this.progress));
    }

    markComplete(experimentId) {
        this.progress[experimentId] = {
            completed: true,
            completedAt: new Date().toISOString()
        };
        this.saveProgress();
        this.updateUI();
    }

    markIncomplete(experimentId) {
        if (this.progress[experimentId]) {
            this.progress[experimentId].completed = false;
        }
        this.saveProgress();
        this.updateUI();
    }

    isCompleted(experimentId) {
        return this.progress[experimentId]?.completed || false;
    }

    getCompletionCount() {
        return Object.values(this.progress).filter(p => p.completed).length;
    }

    updateUI() {
        this.updateProgressBar();
        this.updateExperimentCards();
    }

    updateProgressBar() {
        const completed = this.getCompletionCount();
        const total = experiments.length;
        const percentage = (completed / total) * 100;
        
        const progressBar = document.getElementById('overall-progress');
        const progressText = document.getElementById('progress-text');
        
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${completed}/${total} Complete`;
        }
    }

    updateExperimentCards() {
        experiments.forEach(experiment => {
            const card = document.querySelector(`[data-experiment-id="${experiment.id}"]`);
            if (card) {
                const isCompleted = this.isCompleted(experiment.id);
                card.classList.toggle('completed', isCompleted);
                
                const status = card.querySelector('.experiment-status');
                if (status) {
                    status.textContent = isCompleted ? '✅' : '📚';
                }
            }
        });
    }
}

// Initialize progress tracker
const progressTracker = new ProgressTracker();

// Generate experiment cards
function generateExperimentCards() {
    const grid = document.getElementById('experiments-grid');
    if (!grid) return;

    grid.innerHTML = experiments.map(experiment => {
        const isCompleted = progressTracker.isCompleted(experiment.id);
        
        return `
            <div class="experiment-card ${isCompleted ? 'completed' : ''}" 
                 data-experiment-id="${experiment.id}"
                 onclick="navigateToExperiment('${experiment.path}')">
                <div class="experiment-header">
                    <span class="experiment-number">${experiment.id.toString().padStart(2, '0')}</span>
                    <span class="experiment-status">${isCompleted ? '✅' : '📚'}</span>
                </div>
                <h4 class="experiment-title">${experiment.title}</h4>
                <p class="experiment-description">${experiment.description}</p>
                <div class="experiment-meta">
                    <span class="difficulty ${experiment.difficulty}">${experiment.difficulty}</span>
                    <span class="duration">${experiment.duration}</span>
                </div>
            </div>
        `;
    }).join('');
}

// Navigation function
function navigateToExperiment(path) {
    window.location.href = path;
}

// Initialize Mermaid
function initializeMermaid() {
    if (typeof mermaid !== 'undefined') {
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Inter, sans-serif'
        });
    }
}

// Initialize the application
function initializeApp() {
    generateExperimentCards();
    progressTracker.updateUI();
    initializeMermaid();
    
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Utility functions for experiments
window.AILearningHub = {
    progressTracker,
    experiments,
    markExperimentComplete: (id) => progressTracker.markComplete(id),
    markExperimentIncomplete: (id) => progressTracker.markIncomplete(id),
    getExperiment: (id) => experiments.find(exp => exp.id === id),
    navigateToExperiment
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeApp);
