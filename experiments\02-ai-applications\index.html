<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Experiment 2: AI Applications in the Real World | AI Learning Hub</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../../css/main.css">
    <link rel="stylesheet" href="../../css/experiments.css">
    <link rel="stylesheet" href="../../css/responsive.css">
    
    <!-- External Libraries -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
</head>
<body data-experiment-id="2">
    <!-- Navigation Header -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="../../index.html" style="text-decoration: none; color: inherit;">
                        <h1>🤖 AI Learning Hub</h1>
                    </a>
                    <span class="tagline">Experiment 2: AI Applications</span>
                </div>
                <div class="nav-progress">
                    <a href="../../index.html" class="nav-btn secondary">← Back to Hub</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <div class="experiment-container">
        <!-- Sidebar -->
        <aside class="experiment-sidebar">
            <!-- Progress -->
            <div class="experiment-progress">
                <div class="progress-label">Progress</div>
                <div class="progress-bar-container">
                    <div class="progress-bar-fill"></div>
                </div>
            </div>

            <!-- Step Navigation -->
            <div class="step-navigation">
                <h4 class="step-nav-title">Sections</h4>
                <ul class="step-list">
                    <li class="step-item">
                        <a href="#step1" class="step-link">1. AI in Healthcare</a>
                    </li>
                    <li class="step-item">
                        <a href="#step2" class="step-link">2. AI in Transportation</a>
                    </li>
                    <li class="step-item">
                        <a href="#step3" class="step-link">3. AI for Renewable Energy</a>
                    </li>
                    <li class="step-item">
                        <a href="#step4" class="step-link">4. AI for Pollution Control</a>
                    </li>
                    <li class="step-item">
                        <a href="#step5" class="step-link">5. Implementation Examples</a>
                    </li>
                    <li class="step-item">
                        <a href="#step6" class="step-link">6. Future Possibilities</a>
                    </li>
                </ul>
            </div>

            <!-- Prerequisites -->
            <div class="prerequisites">
                <h4 class="section-title">Prerequisites</h4>
                <ul class="prereq-list">
                    <li class="prereq-item">Basic understanding of AI concepts</li>
                    <li class="prereq-item">Completed Experiment 1 (Chatbot)</li>
                    <li class="prereq-item">Curiosity about real-world applications</li>
                </ul>
            </div>

            <!-- Next Steps -->
            <div class="next-steps">
                <h4 class="section-title">Next Steps</h4>
                <ul class="next-list">
                    <li class="next-item">Search Algorithms</li>
                    <li class="next-item">Problem-Solving Techniques</li>
                    <li class="next-item">Pattern Recognition</li>
                </ul>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="experiment-content">
            <!-- Experiment Header -->
            <div class="experiment-header">
                <h1 class="experiment-title">AI Applications in the Real World</h1>
                <p class="experiment-subtitle">Explore how artificial intelligence is transforming healthcare, transportation, renewable energy, and environmental protection</p>
                <div class="experiment-meta">
                    <div class="meta-item">
                        <span class="meta-icon">⏱️</span>
                        <span>45 minutes</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">📊</span>
                        <span>Beginner</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">🎯</span>
                        <span>Real-World AI</span>
                    </div>
                </div>
            </div>

            <!-- Step 1: AI in Healthcare -->
            <section class="content-section" id="step1">
                <div class="section-header">
                    <div class="section-number">1</div>
                    <h2 class="section-title-main">AI in Healthcare</h2>
                </div>

                <p>Artificial Intelligence is revolutionizing healthcare by improving diagnosis accuracy, accelerating drug discovery, and personalizing treatment plans. Let's explore the key applications and their impact.</p>

                <h3>Medical Imaging and Diagnosis</h3>
                <p>AI systems can analyze medical images with superhuman accuracy, detecting diseases earlier than traditional methods.</p>

                <div class="diagram-container">
                    <h4 class="diagram-title">AI Medical Diagnosis Process</h4>
                    <div class="mermaid">
                        graph TD
                            A[Medical Image] --> B[AI Image Analysis]
                            B --> C[Feature Extraction]
                            C --> D[Pattern Recognition]
                            D --> E{Abnormality Detected?}
                            E -->|Yes| F[Flag for Review]
                            E -->|No| G[Normal Result]
                            F --> H[Doctor Verification]
                            G --> I[Report Generated]
                            H --> I
                            I --> J[Treatment Plan]
                    </div>
                </div>

                <div class="interactive-demo" data-demo-type="healthcare">
                    <h4 class="demo-title">🏥 AI Diagnosis Simulator</h4>
                    <p>Select symptoms to see how AI might assist in diagnosis:</p>
                    <div class="demo-controls">
                        <label><input type="checkbox" class="symptom-check" value="fever"> Fever</label>
                        <label><input type="checkbox" class="symptom-check" value="cough"> Cough</label>
                        <label><input type="checkbox" class="symptom-check" value="fatigue"> Fatigue</label>
                        <label><input type="checkbox" class="symptom-check" value="headache"> Headache</label>
                        <button class="demo-button" onclick="runHealthcareDiagnosis()">Analyze Symptoms</button>
                    </div>
                    <div class="demo-output">Select symptoms and click 'Analyze Symptoms' to see AI-assisted diagnosis suggestions.</div>
                </div>

                <h3>Key Healthcare AI Applications</h3>
                <ul>
                    <li><strong>Radiology:</strong> Detecting tumors in X-rays, MRIs, and CT scans</li>
                    <li><strong>Drug Discovery:</strong> Accelerating the development of new medications</li>
                    <li><strong>Personalized Medicine:</strong> Tailoring treatments based on genetic profiles</li>
                    <li><strong>Robotic Surgery:</strong> Precision operations with minimal invasiveness</li>
                    <li><strong>Mental Health:</strong> Early detection of depression and anxiety</li>
                </ul>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">Simple Health Risk Calculator</span>
                        <div class="code-actions">
                            <button class="code-btn copy-btn">Copy</button>
                            <button class="code-btn run-btn">Run</button>
                        </div>
                    </div>
                    <pre><code class="language-python">def calculate_health_risk(age, bmi, smoking, exercise_hours):
    """
    Simple AI-inspired health risk assessment
    """
    risk_score = 0
    
    # Age factor
    if age > 65:
        risk_score += 3
    elif age > 45:
        risk_score += 2
    elif age > 30:
        risk_score += 1
    
    # BMI factor
    if bmi > 30:
        risk_score += 3
    elif bmi > 25:
        risk_score += 2
    
    # Smoking factor
    if smoking:
        risk_score += 4
    
    # Exercise factor
    if exercise_hours < 2:
        risk_score += 2
    elif exercise_hours < 4:
        risk_score += 1
    
    # Risk assessment
    if risk_score >= 8:
        return "High Risk - Consult healthcare provider"
    elif risk_score >= 5:
        return "Moderate Risk - Consider lifestyle changes"
    elif risk_score >= 2:
        return "Low Risk - Maintain healthy habits"
    else:
        return "Very Low Risk - Excellent health profile"

# Example usage
patient_risk = calculate_health_risk(
    age=45, 
    bmi=26.5, 
    smoking=False, 
    exercise_hours=3
)
print(f"Health Risk Assessment: {patient_risk}")</code></pre>
                </div>
            </section>

            <!-- Step 2: AI in Transportation -->
            <section class="content-section" id="step2" style="display: none;">
                <div class="section-header">
                    <div class="section-number">2</div>
                    <h2 class="section-title-main">AI in Transportation</h2>
                </div>

                <p>Transportation is being transformed by AI through autonomous vehicles, traffic optimization, and predictive maintenance systems.</p>

                <div class="diagram-container">
                    <h4 class="diagram-title">Autonomous Vehicle AI System</h4>
                    <div class="mermaid">
                        graph TD
                            A[Sensors] --> B[Data Collection]
                            B --> C[Computer Vision]
                            B --> D[LIDAR Processing]
                            B --> E[GPS Navigation]
                            C --> F[Object Detection]
                            D --> G[Distance Mapping]
                            E --> H[Route Planning]
                            F --> I[Decision Engine]
                            G --> I
                            H --> I
                            I --> J[Vehicle Control]
                            J --> K[Steering/Braking/Acceleration]
                    </div>
                </div>

                <div class="interactive-demo" data-demo-type="transportation">
                    <h4 class="demo-title">🚗 Traffic Optimization Simulator</h4>
                    <p>Adjust traffic parameters to see AI optimization in action:</p>
                    <div class="demo-controls">
                        <label>Traffic Volume: <input type="range" id="traffic-volume" min="1" max="10" value="5"></label>
                        <label>Weather Condition:
                            <select id="weather">
                                <option value="clear">Clear</option>
                                <option value="rain">Rain</option>
                                <option value="snow">Snow</option>
                            </select>
                        </label>
                        <button class="demo-button" onclick="optimizeTraffic()">Optimize Traffic</button>
                    </div>
                    <div class="demo-output">Adjust parameters and click 'Optimize Traffic' to see AI recommendations.</div>
                </div>

                <h3>Transportation AI Applications</h3>
                <ul>
                    <li><strong>Autonomous Vehicles:</strong> Self-driving cars and trucks</li>
                    <li><strong>Traffic Management:</strong> Smart traffic lights and route optimization</li>
                    <li><strong>Predictive Maintenance:</strong> Preventing vehicle breakdowns</li>
                    <li><strong>Ride Sharing:</strong> Optimal driver-passenger matching</li>
                    <li><strong>Aviation:</strong> Flight path optimization and safety systems</li>
                </ul>
            </section>

            <!-- Step 3: AI for Renewable Energy -->
            <section class="content-section" id="step3" style="display: none;">
                <div class="section-header">
                    <div class="section-number">3</div>
                    <h2 class="section-title-main">AI for Renewable Energy</h2>
                </div>

                <p>AI is optimizing renewable energy systems by predicting weather patterns, managing smart grids, and maximizing energy efficiency.</p>

                <div class="diagram-container">
                    <h4 class="diagram-title">Smart Grid AI Management</h4>
                    <div class="mermaid">
                        graph TD
                            A[Solar Panels] --> D[Smart Grid AI]
                            B[Wind Turbines] --> D
                            C[Energy Storage] --> D
                            D --> E[Demand Prediction]
                            D --> F[Supply Optimization]
                            D --> G[Grid Balancing]
                            E --> H[Energy Distribution]
                            F --> H
                            G --> H
                            H --> I[Homes & Businesses]
                    </div>
                </div>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">Solar Energy Prediction Model</span>
                        <div class="code-actions">
                            <button class="code-btn copy-btn">Copy</button>
                            <button class="code-btn run-btn">Run</button>
                        </div>
                    </div>
                    <pre><code class="language-python">def predict_solar_energy(cloud_cover, temperature, hour_of_day):
    """
    Simple AI model to predict solar energy output
    """
    # Base efficiency factors
    base_efficiency = 0.8

    # Cloud cover impact (0-100%)
    cloud_factor = 1 - (cloud_cover / 100)

    # Temperature impact (optimal around 25°C)
    temp_factor = 1 - abs(temperature - 25) * 0.01
    temp_factor = max(0.5, temp_factor)  # Minimum 50% efficiency

    # Time of day impact (peak at noon)
    if 6 <= hour_of_day <= 18:  # Daylight hours
        time_factor = 1 - abs(hour_of_day - 12) * 0.08
    else:
        time_factor = 0  # No solar energy at night

    # Calculate predicted energy output (kW)
    max_capacity = 100  # kW
    predicted_output = max_capacity * base_efficiency * cloud_factor * temp_factor * time_factor

    return max(0, predicted_output)

# Example predictions for different conditions
conditions = [
    (10, 25, 12),  # Clear day at noon
    (70, 30, 14),  # Cloudy afternoon
    (20, 15, 8),   # Clear morning
    (0, 35, 16)    # Very hot, clear afternoon
]

print("Solar Energy Predictions:")
for cloud, temp, hour in conditions:
    energy = predict_solar_energy(cloud, temp, hour)
    print(f"Clouds: {cloud}%, Temp: {temp}°C, Hour: {hour}:00 → {energy:.1f} kW")</code></pre>
                </div>

                <h3>Renewable Energy AI Applications</h3>
                <ul>
                    <li><strong>Weather Forecasting:</strong> Predicting solar and wind conditions</li>
                    <li><strong>Grid Management:</strong> Balancing supply and demand</li>
                    <li><strong>Energy Storage:</strong> Optimizing battery usage</li>
                    <li><strong>Maintenance:</strong> Predicting equipment failures</li>
                    <li><strong>Efficiency:</strong> Maximizing energy conversion rates</li>
                </ul>
            </section>

            <!-- Step 4: AI for Pollution Control -->
            <section class="content-section" id="step4" style="display: none;">
                <div class="section-header">
                    <div class="section-number">4</div>
                    <h2 class="section-title-main">AI for Pollution Control</h2>
                </div>

                <p>AI is helping combat pollution through monitoring systems, predictive models, and optimization of industrial processes.</p>

                <div class="diagram-container">
                    <h4 class="diagram-title">AI Pollution Monitoring Network</h4>
                    <div class="mermaid">
                        graph TD
                            A[Air Quality Sensors] --> E[AI Processing Center]
                            B[Water Quality Sensors] --> E
                            C[Noise Level Monitors] --> E
                            D[Satellite Data] --> E
                            E --> F[Pattern Analysis]
                            E --> G[Pollution Prediction]
                            E --> H[Source Identification]
                            F --> I[Alert System]
                            G --> I
                            H --> I
                            I --> J[Government Agencies]
                            I --> K[Public Warnings]
                            I --> L[Industry Notifications]
                    </div>
                </div>

                <h3>Pollution Control AI Applications</h3>
                <ul>
                    <li><strong>Air Quality Monitoring:</strong> Real-time pollution tracking and prediction</li>
                    <li><strong>Industrial Optimization:</strong> Reducing emissions through process improvement</li>
                    <li><strong>Waste Management:</strong> Optimizing collection routes and recycling</li>
                    <li><strong>Water Treatment:</strong> Monitoring and controlling water quality</li>
                    <li><strong>Carbon Footprint:</strong> Tracking and reducing greenhouse gas emissions</li>
                </ul>

                <div class="exercise-box">
                    <h4 class="exercise-title">🌍 Environmental Impact</h4>
                    <div class="exercise-content">
                        <p>AI pollution control systems have achieved remarkable results:</p>
                        <ul>
                            <li>30% reduction in industrial emissions through optimization</li>
                            <li>25% improvement in air quality prediction accuracy</li>
                            <li>40% more efficient waste collection routes</li>
                            <li>Real-time alerts preventing environmental disasters</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Step 5: Implementation Examples -->
            <section class="content-section" id="step5" style="display: none;">
                <div class="section-header">
                    <div class="section-number">5</div>
                    <h2 class="section-title-main">Implementation Examples</h2>
                </div>

                <p>Let's look at real-world examples of how these AI applications are implemented in practice.</p>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">Multi-Domain AI Decision System</span>
                        <div class="code-actions">
                            <button class="code-btn copy-btn">Copy</button>
                            <button class="code-btn run-btn">Run</button>
                        </div>
                    </div>
                    <pre><code class="language-python">class AIApplicationSystem:
    """
    Integrated AI system demonstrating multiple domain applications
    """

    def __init__(self):
        self.healthcare_data = []
        self.traffic_data = []
        self.energy_data = []
        self.pollution_data = []

    def healthcare_diagnosis(self, symptoms, age, medical_history):
        """AI-assisted medical diagnosis"""
        risk_score = 0

        # Analyze symptoms
        high_risk_symptoms = ['chest_pain', 'difficulty_breathing', 'severe_headache']
        for symptom in symptoms:
            if symptom in high_risk_symptoms:
                risk_score += 3
            else:
                risk_score += 1

        # Age factor
        if age > 65:
            risk_score += 2

        # Medical history
        if 'diabetes' in medical_history or 'heart_disease' in medical_history:
            risk_score += 2

        if risk_score >= 7:
            return "High priority - Immediate medical attention recommended"
        elif risk_score >= 4:
            return "Medium priority - Schedule appointment within 24 hours"
        else:
            return "Low priority - Monitor symptoms, routine care"

    def traffic_optimization(self, current_flow, weather, time_of_day):
        """AI traffic management system"""
        base_timing = 30  # seconds

        # Adjust for traffic flow
        timing_adjustment = current_flow * 2

        # Weather adjustments
        weather_factor = {'clear': 0, 'rain': 5, 'snow': 10}
        timing_adjustment += weather_factor.get(weather, 0)

        # Time of day adjustments
        if 7 <= time_of_day <= 9 or 17 <= time_of_day <= 19:  # Rush hours
            timing_adjustment += 10

        optimal_timing = base_timing + timing_adjustment
        return f"Optimal light timing: {optimal_timing} seconds"

    def energy_prediction(self, solar_irradiance, wind_speed, demand_forecast):
        """AI renewable energy management"""
        solar_output = solar_irradiance * 0.8  # 80% efficiency
        wind_output = min(wind_speed * 10, 100)  # Max 100kW

        total_renewable = solar_output + wind_output

        if total_renewable >= demand_forecast:
            return f"Surplus energy: {total_renewable - demand_forecast:.1f}kW - Store in batteries"
        else:
            deficit = demand_forecast - total_renewable
            return f"Energy deficit: {deficit:.1f}kW - Use grid backup"

    def pollution_alert(self, air_quality_index, pollution_sources):
        """AI pollution monitoring and alerts"""
        if air_quality_index > 150:
            alert_level = "Unhealthy"
            action = "Reduce outdoor activities"
        elif air_quality_index > 100:
            alert_level = "Moderate"
            action = "Sensitive groups should limit outdoor exposure"
        else:
            alert_level = "Good"
            action = "Normal activities"

        # Identify main pollution source
        main_source = max(pollution_sources, key=pollution_sources.get)

        return f"Air Quality: {alert_level} (AQI: {air_quality_index})\n" \
               f"Main source: {main_source}\n" \
               f"Recommendation: {action}"

# Example usage
ai_system = AIApplicationSystem()

# Healthcare example
health_result = ai_system.healthcare_diagnosis(
    symptoms=['fever', 'cough'],
    age=45,
    medical_history=['diabetes']
)
print("Healthcare AI:", health_result)

# Traffic example
traffic_result = ai_system.traffic_optimization(
    current_flow=8,
    weather='rain',
    time_of_day=8
)
print("Traffic AI:", traffic_result)

# Energy example
energy_result = ai_system.energy_prediction(
    solar_irradiance=75,
    wind_speed=12,
    demand_forecast=150
)
print("Energy AI:", energy_result)

# Pollution example
pollution_result = ai_system.pollution_alert(
    air_quality_index=85,
    pollution_sources={'vehicles': 40, 'industry': 35, 'construction': 25}
)
print("Pollution AI:", pollution_result)</code></pre>
                </div>
            </section>

            <!-- Step 6: Future Possibilities -->
            <section class="content-section" id="step6" style="display: none;">
                <div class="section-header">
                    <div class="section-number">6</div>
                    <h2 class="section-title-main">Future Possibilities</h2>
                </div>

                <p>The future of AI applications holds even more exciting possibilities across all these domains.</p>

                <h3>Emerging Trends</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin: 2rem 0;">
                    <div style="background: var(--bg-secondary); padding: 1.5rem; border-radius: var(--border-radius-lg);">
                        <h4 style="color: var(--primary-color);">🏥 Healthcare 2030</h4>
                        <ul>
                            <li>AI doctors for routine consultations</li>
                            <li>Personalized medicine based on DNA</li>
                            <li>Nano-robots for targeted drug delivery</li>
                            <li>Brain-computer interfaces for disabilities</li>
                        </ul>
                    </div>
                    <div style="background: var(--bg-secondary); padding: 1.5rem; border-radius: var(--border-radius-lg);">
                        <h4 style="color: var(--primary-color);">🚗 Transportation 2030</h4>
                        <ul>
                            <li>Fully autonomous vehicle fleets</li>
                            <li>Flying cars with AI navigation</li>
                            <li>Hyperloop transportation systems</li>
                            <li>Space travel optimization</li>
                        </ul>
                    </div>
                    <div style="background: var(--bg-secondary); padding: 1.5rem; border-radius: var(--border-radius-lg);">
                        <h4 style="color: var(--primary-color);">⚡ Energy 2030</h4>
                        <ul>
                            <li>100% renewable energy grids</li>
                            <li>Fusion power optimization</li>
                            <li>Wireless energy transmission</li>
                            <li>AI-designed solar panels</li>
                        </ul>
                    </div>
                    <div style="background: var(--bg-secondary); padding: 1.5rem; border-radius: var(--border-radius-lg);">
                        <h4 style="color: var(--primary-color);">🌍 Environment 2030</h4>
                        <ul>
                            <li>AI-controlled carbon capture</li>
                            <li>Ocean cleanup robots</li>
                            <li>Weather modification systems</li>
                            <li>Ecosystem restoration AI</li>
                        </ul>
                    </div>
                </div>

                <div class="exercise-box">
                    <h4 class="exercise-title">🚀 Your AI Future</h4>
                    <div class="exercise-content">
                        <p><strong>Reflection Questions:</strong></p>
                        <ul>
                            <li>Which AI application excites you the most and why?</li>
                            <li>What ethical considerations should we keep in mind?</li>
                            <li>How might you contribute to AI development in these fields?</li>
                            <li>What new AI applications can you imagine?</li>
                        </ul>
                        <p><strong>Next Steps:</strong> Consider how the programming concepts you're learning could be applied to solve real-world problems in these domains!</p>
                    </div>
                </div>

                <h3>Key Takeaways</h3>
                <ul>
                    <li>AI is already transforming healthcare, transportation, energy, and environmental protection</li>
                    <li>These applications use the same fundamental programming concepts you're learning</li>
                    <li>Real-world AI systems combine multiple techniques and data sources</li>
                    <li>The future holds even more exciting possibilities for AI applications</li>
                    <li>Understanding these applications helps motivate learning AI programming skills</li>
                </ul>
            </section>

            <!-- Navigation Buttons -->
            <div class="nav-buttons">
                <a href="../01-chatbot/index.html" class="nav-btn prev">← Previous Experiment</a>
                <button class="nav-btn complete-experiment">Complete Experiment</button>
                <a href="../03-greedy-search/index.html" class="nav-btn next">Next Experiment →</a>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="../../js/experiment.js"></script>
    <script>
        function runHealthcareDiagnosis() {
            const symptoms = Array.from(document.querySelectorAll('.symptom-check:checked')).map(cb => cb.value);
            const output = document.querySelector('.demo-output');
            
            if (symptoms.length === 0) {
                output.textContent = "Please select at least one symptom for analysis.";
                return;
            }
            
            let diagnosis = "AI Analysis Results:\n\n";
            diagnosis += `Symptoms detected: ${symptoms.join(', ')}\n\n`;
            
            // Simple rule-based diagnosis simulation
            if (symptoms.includes('fever') && symptoms.includes('cough')) {
                diagnosis += "Possible conditions:\n• Respiratory infection (70% confidence)\n• Common cold (60% confidence)\n\nRecommendation: Rest, fluids, monitor temperature";
            } else if (symptoms.includes('headache') && symptoms.includes('fatigue')) {
                diagnosis += "Possible conditions:\n• Stress/tension (65% confidence)\n• Dehydration (55% confidence)\n\nRecommendation: Rest, hydration, stress management";
            } else if (symptoms.includes('fever')) {
                diagnosis += "Possible conditions:\n• Viral infection (60% confidence)\n• Bacterial infection (40% confidence)\n\nRecommendation: Monitor symptoms, seek medical advice if persistent";
            } else {
                diagnosis += "Symptoms suggest minor health concerns.\n\nRecommendation: Monitor symptoms, maintain healthy lifestyle";
            }
            
            diagnosis += "\n\n⚠️ Note: This is a simplified demonstration. Always consult healthcare professionals for real medical concerns.";
            output.textContent = diagnosis;
        }

        function optimizeTraffic() {
            const volume = document.getElementById('traffic-volume').value;
            const weather = document.getElementById('weather').value;
            const output = document.querySelector('#step2 .demo-output');

            let optimization = "AI Traffic Optimization Results:\n\n";
            optimization += `Traffic Volume: ${volume}/10\n`;
            optimization += `Weather: ${weather}\n\n`;

            // Calculate optimal settings
            let lightTiming = 30; // base seconds
            let speedLimit = 50; // base km/h
            let routeEfficiency = 85; // base percentage

            // Adjust for traffic volume
            lightTiming += parseInt(volume) * 2;
            if (volume > 7) {
                speedLimit -= 10;
                routeEfficiency -= 15;
            }

            // Adjust for weather
            if (weather === 'rain') {
                speedLimit -= 10;
                lightTiming += 5;
                routeEfficiency -= 10;
            } else if (weather === 'snow') {
                speedLimit -= 20;
                lightTiming += 10;
                routeEfficiency -= 20;
            }

            optimization += `Optimized Settings:\n`;
            optimization += `• Traffic Light Timing: ${lightTiming} seconds\n`;
            optimization += `• Recommended Speed Limit: ${speedLimit} km/h\n`;
            optimization += `• Route Efficiency: ${routeEfficiency}%\n\n`;

            optimization += `AI Recommendations:\n`;
            if (volume > 8) {
                optimization += "• Activate alternate routes\n• Deploy traffic officers\n";
            }
            if (weather !== 'clear') {
                optimization += "• Increase following distance warnings\n• Activate weather alerts\n";
            }
            optimization += "• Monitor congestion in real-time\n• Adjust signals dynamically";

            output.textContent = optimization;
        }
    </script>
</body>
</html>
