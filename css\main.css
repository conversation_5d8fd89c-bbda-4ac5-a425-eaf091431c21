/* AI Learning Hub - Main Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-sans);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

/* Header and Navigation */
.main-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 0;
}

.tagline {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 400;
}

.nav-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-container {
    width: 200px;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Hero Section */
.hero {
    padding: 4rem 0;
    text-align: center;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    margin: 0 -2rem 4rem -2rem;
}

.hero-content h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.hero-content p {
    font-size: 1.125rem;
    max-width: 600px;
    margin: 0 auto 2rem auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Learning Path */
.learning-path {
    margin-bottom: 4rem;
}

.learning-path h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.path-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.path-level {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.path-level:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.path-level h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Experiments Grid */
.experiments-section {
    margin-bottom: 4rem;
}

.experiments-section h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.experiments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

/* Experiment Card Styles */
.experiment-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.experiment-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.experiment-card.completed {
    border-color: var(--secondary-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, #f0fdf4 100%);
}

.experiment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.experiment-number {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.experiment-status {
    font-size: 1.5rem;
}

.experiment-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.experiment-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.experiment-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-light);
}

.difficulty {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
}

.difficulty.beginner {
    background: #dbeafe;
    color: #1e40af;
}

.difficulty.intermediate {
    background: #fef3c7;
    color: #92400e;
}

.difficulty.advanced {
    background: #fce7f3;
    color: #be185d;
}

/* Resources Section */
.resources {
    margin-bottom: 4rem;
}

.resources h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.resource-card {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
}

.resource-card h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Footer */
.main-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Utility Classes */
.text-center { text-align: center; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 2rem; }
