/* Responsive Design for AI Learning Hub */

/* Mobile First Approach */

/* Small devices (landscape phones, 576px and up) */
@media (max-width: 576px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }
    
    .nav-progress {
        width: 100%;
        justify-content: center;
    }
    
    .progress-container {
        width: 150px;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .hero {
        margin: 0 -1rem 2rem -1rem;
        padding: 2rem 1rem;
    }
    
    .hero-content h2 {
        font-size: 1.5rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .path-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .path-level {
        padding: 1.5rem;
    }
    
    .experiments-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .experiment-card {
        padding: 1rem;
    }
    
    .resources-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .resource-card {
        padding: 1.5rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 0 1rem;
    }
    
    .footer-links {
        gap: 1rem;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 577px) and (max-width: 768px) {
    .nav-container {
        padding: 0 1.5rem;
    }
    
    .main-content {
        padding: 0 1.5rem;
    }
    
    .hero {
        margin: 0 -1.5rem 3rem -1.5rem;
        padding: 3rem 1.5rem;
    }
    
    .hero-stats {
        gap: 2rem;
    }
    
    .path-container {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .experiments-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
    
    .resources-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .footer-content {
        padding: 0 1.5rem;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 769px) and (max-width: 1200px) {
    .experiments-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1201px) {
    .experiments-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
    .hero {
        padding: 2rem 0;
    }
    
    .hero-content h2 {
        font-size: 1.75rem;
    }
    
    .hero-stats {
        margin-top: 1rem;
    }
    
    .path-level {
        padding: 1.5rem;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .experiment-card {
        border-width: 0.5px;
    }
    
    .main-header {
        border-bottom-width: 0.5px;
    }
    
    .main-footer {
        border-top-width: 0.5px;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .experiment-card:hover {
        transform: none;
    }
    
    .path-level:hover {
        transform: none;
    }
}

/* Dark mode support (if user prefers dark color scheme) */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-light: #9ca3af;
        --bg-primary: #111827;
        --bg-secondary: #1f2937;
        --bg-tertiary: #374151;
        --border-color: #374151;
    }
    
    .hero {
        background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    }
    
    .experiment-card.completed {
        background: linear-gradient(135deg, var(--bg-primary) 0%, #064e3b 100%);
    }
}

/* Print styles */
@media print {
    .main-header,
    .main-footer {
        display: none;
    }
    
    .main-content {
        max-width: none;
        padding: 0;
    }
    
    .experiment-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .hero {
        margin: 0;
        padding: 1rem 0;
        background: none;
    }
    
    a {
        text-decoration: underline;
    }
    
    .experiments-grid {
        grid-template-columns: 1fr;
    }
}

/* Focus styles for accessibility */
@media (prefers-reduced-motion: no-preference) {
    .experiment-card:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    .footer-links a:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
}

/* Container queries for modern browsers */
@supports (container-type: inline-size) {
    .experiments-grid {
        container-type: inline-size;
    }
    
    @container (max-width: 400px) {
        .experiment-card {
            padding: 1rem;
        }
        
        .experiment-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
}
