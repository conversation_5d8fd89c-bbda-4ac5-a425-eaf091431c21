// Experiment Page JavaScript Functionality

class ExperimentPage {
    constructor(experimentId) {
        this.experimentId = experimentId;
        this.currentStep = 1;
        this.totalSteps = 0;
        this.progressTracker = window.parent?.AILearningHub?.progressTracker || this.createLocalTracker();
        this.init();
    }

    createLocalTracker() {
        return {
            markComplete: (id) => console.log(`Experiment ${id} completed`),
            isCompleted: (id) => false
        };
    }

    init() {
        this.setupMermaid();
        this.setupCodeHighlighting();
        this.setupInteractiveElements();
        this.setupNavigation();
        this.updateProgress();
        this.setupStepNavigation();
    }

    setupMermaid() {
        if (typeof mermaid !== 'undefined') {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                fontFamily: 'Inter, sans-serif',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                }
            });
        }
    }

    setupCodeHighlighting() {
        if (typeof Prism !== 'undefined') {
            Prism.highlightAll();
        }
        
        // Add copy functionality to code blocks
        document.querySelectorAll('.code-container').forEach(container => {
            const copyBtn = container.querySelector('.copy-btn');
            if (copyBtn) {
                copyBtn.addEventListener('click', () => this.copyCode(container));
            }
            
            const runBtn = container.querySelector('.run-btn');
            if (runBtn) {
                runBtn.addEventListener('click', () => this.runCode(container));
            }
        });
    }

    copyCode(container) {
        const code = container.querySelector('code');
        if (code) {
            navigator.clipboard.writeText(code.textContent).then(() => {
                this.showNotification('Code copied to clipboard!', 'success');
            }).catch(() => {
                this.showNotification('Failed to copy code', 'error');
            });
        }
    }

    runCode(container) {
        const code = container.querySelector('code');
        const output = container.querySelector('.code-output') || this.createOutputElement(container);
        
        if (code) {
            // Simulate code execution (in a real implementation, you'd use a Python interpreter)
            output.textContent = 'Running code...\n';
            
            setTimeout(() => {
                try {
                    const result = this.simulateCodeExecution(code.textContent);
                    output.textContent = result;
                } catch (error) {
                    output.textContent = `Error: ${error.message}`;
                }
            }, 1000);
        }
    }

    createOutputElement(container) {
        const output = document.createElement('div');
        output.className = 'code-output';
        output.style.cssText = `
            background: #1a202c;
            color: #e2e8f0;
            padding: 1rem;
            margin-top: 1rem;
            border-radius: 4px;
            font-family: var(--font-family-mono);
            font-size: 0.875rem;
            white-space: pre-wrap;
            border: 1px solid var(--border-color);
        `;
        container.appendChild(output);
        return output;
    }

    simulateCodeExecution(code) {
        // Simple simulation - in a real app, you'd use Pyodide or similar
        if (code.includes('print(')) {
            const matches = code.match(/print\((.*?)\)/g);
            if (matches) {
                return matches.map(match => {
                    const content = match.replace(/print\(['"]?(.*?)['"]?\)/, '$1');
                    return content;
                }).join('\n');
            }
        }
        return 'Code executed successfully!\n(Note: This is a simulation)';
    }

    setupInteractiveElements() {
        // Setup interactive demos
        document.querySelectorAll('.interactive-demo').forEach(demo => {
            const runBtn = demo.querySelector('.demo-button');
            if (runBtn) {
                runBtn.addEventListener('click', () => this.runDemo(demo));
            }
        });

        // Setup exercise completion
        document.querySelectorAll('.exercise-box').forEach(exercise => {
            const completeBtn = exercise.querySelector('.complete-exercise');
            if (completeBtn) {
                completeBtn.addEventListener('click', () => this.completeExercise(exercise));
            }
        });
    }

    runDemo(demo) {
        const output = demo.querySelector('.demo-output');
        const inputs = demo.querySelectorAll('.demo-input');
        
        if (output) {
            output.textContent = 'Running demo...\n';
            
            setTimeout(() => {
                const inputValues = Array.from(inputs).map(input => input.value);
                const result = this.processDemoInputs(demo.dataset.demoType, inputValues);
                output.textContent = result;
            }, 500);
        }
    }

    processDemoInputs(demoType, inputs) {
        switch (demoType) {
            case 'chatbot':
                return this.processChatbotInput(inputs[0]);
            case 'search':
                return `Searching for path from ${inputs[0]} to ${inputs[1]}...\nPath found: ${inputs[0]} -> Node2 -> ${inputs[1]}`;
            case 'logic':
                return `Evaluating: ${inputs[0]}\nResult: True`;
            default:
                return `Demo result: ${inputs.join(', ')}`;
        }
    }

    processChatbotInput(userInput) {
        if (!userInput) return "Bot: Please type something!";

        const input = userInput.toLowerCase().trim();

        // Greeting responses
        if (input.includes('hello') || input.includes('hi') || input.includes('hey')) {
            const greetings = [
                "Hello! Great to see you here!",
                "Hi there! Ready to learn about AI?",
                "Hey! Welcome to our AI learning session!"
            ];
            return `Bot: ${greetings[Math.floor(Math.random() * greetings.length)]}`;
        }

        // AI-related questions
        if (input.includes('ai') || input.includes('artificial intelligence')) {
            return "Bot: AI is fascinating! It's about creating machines that can think and learn like humans. What aspect interests you most?";
        }

        // Learning questions
        if (input.includes('learn') || input.includes('study') || input.includes('understand')) {
            return "Bot: Learning is awesome! I'm here to help you understand AI concepts step by step. What would you like to explore?";
        }

        // Programming questions
        if (input.includes('python') || input.includes('programming') || input.includes('code')) {
            return "Bot: Python is perfect for AI! It's readable, powerful, and has amazing libraries. Are you new to programming?";
        }

        // Emotional responses
        if (input.includes('confused') || input.includes('difficult') || input.includes('hard')) {
            return "Bot: Don't worry! Learning can be challenging, but you're doing great. Let's break it down into smaller steps.";
        }

        if (input.includes('excited') || input.includes('fun') || input.includes('cool')) {
            return "Bot: That's the spirit! AI is incredibly exciting. Your enthusiasm will take you far!";
        }

        // Questions about the bot
        if (input.includes('what can you do')) {
            return "Bot: I can help you learn AI concepts, answer questions about programming, and guide you through experiments!";
        }

        // Farewell
        if (input.includes('bye') || input.includes('goodbye') || input.includes('see you')) {
            const farewells = [
                "Goodbye! Keep exploring AI!",
                "See you later! Happy learning!",
                "Bye! Remember, every expert was once a beginner!"
            ];
            return `Bot: ${farewells[Math.floor(Math.random() * farewells.length)]}`;
        }

        // Default responses
        const defaults = [
            "That's interesting! Tell me more about that.",
            "I'd love to help! Could you be more specific?",
            "Hmm, I'm not sure about that. Can you rephrase?",
            "That's a great point! What else would you like to know?"
        ];
        return `Bot: ${defaults[Math.floor(Math.random() * defaults.length)]}`;
    }

    completeExercise(exercise) {
        exercise.style.background = 'linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%)';
        exercise.style.borderColor = '#10b981';
        
        const title = exercise.querySelector('.exercise-title');
        if (title) {
            title.innerHTML = '✅ Exercise Completed!';
            title.style.color = '#065f46';
        }
        
        this.showNotification('Exercise completed!', 'success');
    }

    setupNavigation() {
        // Setup step navigation
        this.totalSteps = document.querySelectorAll('.content-section').length;
        
        // Setup previous/next buttons
        const prevBtn = document.querySelector('.nav-btn.prev');
        const nextBtn = document.querySelector('.nav-btn.next');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateStep(-1);
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateStep(1);
            });
        }

        // Complete experiment button
        const completeBtn = document.querySelector('.complete-experiment');
        if (completeBtn) {
            completeBtn.addEventListener('click', () => this.completeExperiment());
        }
    }

    setupStepNavigation() {
        const stepLinks = document.querySelectorAll('.step-link');
        stepLinks.forEach((link, index) => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToStep(index + 1);
            });
        });
        
        // Highlight current step
        this.updateStepNavigation();
    }

    navigateStep(direction) {
        const newStep = this.currentStep + direction;
        if (newStep >= 1 && newStep <= this.totalSteps) {
            this.goToStep(newStep);
        }
    }

    goToStep(stepNumber) {
        this.currentStep = stepNumber;
        
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.style.display = 'none';
        });
        
        // Show current section
        const currentSection = document.querySelector(`.content-section:nth-child(${stepNumber})`);
        if (currentSection) {
            currentSection.style.display = 'block';
            currentSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
        
        this.updateProgress();
        this.updateStepNavigation();
        this.updateNavigationButtons();
    }

    updateProgress() {
        const progressBar = document.querySelector('.progress-bar-fill');
        if (progressBar) {
            const progress = (this.currentStep / this.totalSteps) * 100;
            progressBar.style.width = `${progress}%`;
        }
        
        const progressLabel = document.querySelector('.progress-label');
        if (progressLabel) {
            progressLabel.textContent = `Step ${this.currentStep} of ${this.totalSteps}`;
        }
    }

    updateStepNavigation() {
        const stepLinks = document.querySelectorAll('.step-link');
        stepLinks.forEach((link, index) => {
            link.classList.remove('active', 'completed');
            
            if (index + 1 === this.currentStep) {
                link.classList.add('active');
            } else if (index + 1 < this.currentStep) {
                link.classList.add('completed');
            }
        });
    }

    updateNavigationButtons() {
        const prevBtn = document.querySelector('.nav-btn.prev');
        const nextBtn = document.querySelector('.nav-btn.next');
        
        if (prevBtn) {
            prevBtn.classList.toggle('disabled', this.currentStep === 1);
        }
        
        if (nextBtn) {
            nextBtn.classList.toggle('disabled', this.currentStep === this.totalSteps);
        }
    }

    completeExperiment() {
        this.progressTracker.markComplete(this.experimentId);
        this.showNotification('Experiment completed! 🎉', 'success');
        
        // Show completion animation or redirect
        setTimeout(() => {
            const nextExperiment = this.experimentId + 1;
            if (nextExperiment <= 12) {
                if (confirm('Great job! Would you like to proceed to the next experiment?')) {
                    window.location.href = `../0${nextExperiment.toString().padStart(2, '0')}-*/index.html`;
                }
            } else {
                alert('Congratulations! You have completed all AI experiments!');
            }
        }, 2000);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            border-radius: 8px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize experiment page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Extract experiment ID from URL or data attribute
    const experimentId = parseInt(document.body.dataset.experimentId) || 1;
    window.experimentPage = new ExperimentPage(experimentId);
});

// Add CSS for notification animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
