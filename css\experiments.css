/* Experiment Page Styles */

/* Experiment Layout */
.experiment-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    min-height: calc(100vh - 120px);
}

.experiment-sidebar {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.experiment-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    border: 1px solid var(--border-color);
}

/* Experiment Header */
.experiment-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.experiment-title {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.experiment-subtitle {
    color: var(--text-secondary);
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.experiment-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--bg-secondary);
    border-radius: 20px;
    font-size: 0.875rem;
}

.meta-icon {
    font-size: 1rem;
}

/* Step Navigation */
.step-navigation {
    margin-bottom: 1.5rem;
}

.step-nav-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.step-list {
    list-style: none;
}

.step-item {
    margin-bottom: 0.5rem;
}

.step-link {
    display: block;
    padding: 0.75rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.step-link:hover,
.step-link.active {
    background: var(--primary-color);
    color: white;
}

.step-link.completed {
    background: var(--secondary-color);
    color: white;
}

/* Prerequisites and Next Steps */
.prerequisites,
.next-steps {
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.prereq-list,
.next-list {
    list-style: none;
}

.prereq-item,
.next-item {
    padding: 0.5rem 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
}

.prereq-item:last-child,
.next-item:last-child {
    border-bottom: none;
}

/* Progress Indicator */
.experiment-progress {
    margin-bottom: 1.5rem;
}

.progress-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.progress-bar-container {
    background: var(--bg-tertiary);
    border-radius: 4px;
    height: 8px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
}

/* Content Sections */
.content-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.section-number {
    background: var(--primary-color);
    color: white;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.section-title-main {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

/* Code Blocks */
.code-container {
    margin: 1.5rem 0;
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.code-header {
    background: var(--bg-tertiary);
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.code-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.code-actions {
    display: flex;
    gap: 0.5rem;
}

.code-btn {
    padding: 0.25rem 0.75rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.code-btn:hover {
    background: var(--primary-dark);
}

.code-btn.secondary {
    background: var(--text-light);
}

.code-btn.secondary:hover {
    background: var(--text-secondary);
}

/* Prism.js Overrides */
pre[class*="language-"] {
    margin: 0;
    padding: 1.5rem;
    background: #2d3748 !important;
    font-family: var(--font-family-mono);
    font-size: 0.875rem;
    line-height: 1.6;
}

code[class*="language-"] {
    font-family: var(--font-family-mono);
}

/* Mermaid Diagrams */
.diagram-container {
    margin: 2rem 0;
    text-align: center;
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.diagram-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.mermaid {
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    display: inline-block;
    box-shadow: var(--shadow-sm);
}

/* Interactive Elements */
.interactive-demo {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid var(--border-color);
}

.demo-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.demo-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.demo-input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: var(--font-family-mono);
    font-size: 0.875rem;
}

.demo-button {
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.demo-button:hover {
    background: var(--primary-dark);
}

.demo-output {
    background: #1a202c;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-family: var(--font-family-mono);
    font-size: 0.875rem;
    white-space: pre-wrap;
    min-height: 100px;
    border: 1px solid var(--border-color);
}

/* Exercise Sections */
.exercise-box {
    background: linear-gradient(135deg, #fef7cd 0%, #fef3c7 100%);
    border: 1px solid #f59e0b;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin: 2rem 0;
}

.exercise-title {
    color: #92400e;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.exercise-content {
    color: #78350f;
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.nav-btn.disabled {
    background: var(--text-light);
    cursor: not-allowed;
    transform: none;
}

.nav-btn.secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.nav-btn.secondary:hover {
    background: var(--bg-secondary);
}
