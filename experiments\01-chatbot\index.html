<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Experiment 1: Simple Text-Based Chatbot | AI Learning Hub</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../../css/main.css">
    <link rel="stylesheet" href="../../css/experiments.css">
    <link rel="stylesheet" href="../../css/responsive.css">
    
    <!-- External Libraries -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
</head>
<body data-experiment-id="1">
    <!-- Navigation Header -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="../../index.html" style="text-decoration: none; color: inherit;">
                        <h1>🤖 AI Learning Hub</h1>
                    </a>
                    <span class="tagline">Experiment 1: Simple Chatbot</span>
                </div>
                <div class="nav-progress">
                    <a href="../../index.html" class="nav-btn secondary">← Back to Hub</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <div class="experiment-container">
        <!-- Sidebar -->
        <aside class="experiment-sidebar">
            <!-- Progress -->
            <div class="experiment-progress">
                <div class="progress-label">Progress</div>
                <div class="progress-bar-container">
                    <div class="progress-bar-fill"></div>
                </div>
            </div>

            <!-- Step Navigation -->
            <div class="step-navigation">
                <h4 class="step-nav-title">Steps</h4>
                <ul class="step-list">
                    <li class="step-item">
                        <a href="#step1" class="step-link">1. Understanding Chatbots</a>
                    </li>
                    <li class="step-item">
                        <a href="#step2" class="step-link">2. Basic If-Else Logic</a>
                    </li>
                    <li class="step-item">
                        <a href="#step3" class="step-link">3. Building the Chatbot</a>
                    </li>
                    <li class="step-item">
                        <a href="#step4" class="step-link">4. Adding Responses</a>
                    </li>
                    <li class="step-item">
                        <a href="#step5" class="step-link">5. Interactive Demo</a>
                    </li>
                    <li class="step-item">
                        <a href="#step6" class="step-link">6. Practice Exercises</a>
                    </li>
                </ul>
            </div>

            <!-- Prerequisites -->
            <div class="prerequisites">
                <h4 class="section-title">Prerequisites</h4>
                <ul class="prereq-list">
                    <li class="prereq-item">Basic Python syntax</li>
                    <li class="prereq-item">Understanding of variables</li>
                    <li class="prereq-item">If-else statements</li>
                    <li class="prereq-item">String operations</li>
                </ul>
            </div>

            <!-- Next Steps -->
            <div class="next-steps">
                <h4 class="section-title">Next Steps</h4>
                <ul class="next-list">
                    <li class="next-item">AI Applications Overview</li>
                    <li class="next-item">Search Algorithms</li>
                    <li class="next-item">Pattern Recognition</li>
                </ul>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="experiment-content">
            <!-- Experiment Header -->
            <div class="experiment-header">
                <h1 class="experiment-title">Simple Text-Based Chatbot</h1>
                <p class="experiment-subtitle">Learn the fundamentals of AI by building your first conversational agent using if-else statements</p>
                <div class="experiment-meta">
                    <div class="meta-item">
                        <span class="meta-icon">⏱️</span>
                        <span>30 minutes</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">📊</span>
                        <span>Beginner</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">🎯</span>
                        <span>Conditional Logic</span>
                    </div>
                </div>
            </div>

            <!-- Step 1: Understanding Chatbots -->
            <section class="content-section" id="step1">
                <div class="section-header">
                    <div class="section-number">1</div>
                    <h2 class="section-title-main">Understanding Chatbots</h2>
                </div>

                <p>A chatbot is a computer program designed to simulate conversation with human users. At its core, a chatbot processes input text and generates appropriate responses based on predefined rules or learned patterns.</p>

                <h3>How Chatbots Work</h3>
                <p>The simplest chatbots use rule-based systems where specific inputs trigger specific outputs. This is exactly what we'll build using if-else statements in Python.</p>

                <div class="diagram-container">
                    <h4 class="diagram-title">Chatbot Flow Diagram</h4>
                    <div class="mermaid">
                        graph TD
                            A[User Input] --> B{Process Input}
                            B --> C[Match Keywords]
                            C --> D{Found Match?}
                            D -->|Yes| E[Generate Response]
                            D -->|No| F[Default Response]
                            E --> G[Display Response]
                            F --> G
                            G --> H[Wait for Next Input]
                            H --> A
                    </div>
                </div>

                <h3>Real-World Applications</h3>
                <ul>
                    <li><strong>Customer Service:</strong> Automated support for common questions</li>
                    <li><strong>Virtual Assistants:</strong> Siri, Alexa, Google Assistant</li>
                    <li><strong>Educational Tools:</strong> Interactive learning companions</li>
                    <li><strong>Healthcare:</strong> Symptom checkers and appointment scheduling</li>
                </ul>
            </section>

            <!-- Step 2: Basic If-Else Logic -->
            <section class="content-section" id="step2" style="display: none;">
                <div class="section-header">
                    <div class="section-number">2</div>
                    <h2 class="section-title-main">Basic If-Else Logic</h2>
                </div>

                <p>Before building our chatbot, let's review the fundamental programming concept that powers it: conditional statements.</p>

                <h3>If-Else Statements in Python</h3>
                <p>If-else statements allow programs to make decisions based on different conditions:</p>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">Basic If-Else Example</span>
                        <div class="code-actions">
                            <button class="code-btn copy-btn">Copy</button>
                            <button class="code-btn run-btn">Run</button>
                        </div>
                    </div>
                    <pre><code class="language-python">user_input = "hello"

if user_input == "hello":
    response = "Hi there! How can I help you?"
elif user_input == "goodbye":
    response = "Goodbye! Have a great day!"
else:
    response = "I'm sorry, I don't understand."

print(response)</code></pre>
                </div>

                <h3>String Matching Techniques</h3>
                <p>For chatbots, we often need to check if certain keywords exist in the user's input:</p>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">Keyword Matching</span>
                        <div class="code-actions">
                            <button class="code-btn copy-btn">Copy</button>
                            <button class="code-btn run-btn">Run</button>
                        </div>
                    </div>
                    <pre><code class="language-python">user_input = "I need help with my order"

if "help" in user_input.lower():
    response = "I'd be happy to help! What do you need assistance with?"
elif "order" in user_input.lower():
    response = "Let me check your order status for you."
elif "weather" in user_input.lower():
    response = "I can't check the weather, but it's always sunny in the world of AI!"
else:
    response = "Could you please rephrase that?"

print(response)</code></pre>
                </div>

                <div class="exercise-box">
                    <h4 class="exercise-title">🎯 Quick Exercise</h4>
                    <div class="exercise-content">
                        <p>Try modifying the code above to respond to different keywords like "price", "shipping", or "return". What happens when multiple keywords are present?</p>
                    </div>
                </div>
            </section>

            <!-- Step 3: Building the Chatbot -->
            <section class="content-section" id="step3" style="display: none;">
                <div class="section-header">
                    <div class="section-number">3</div>
                    <h2 class="section-title-main">Building the Chatbot</h2>
                </div>

                <p>Now let's create our first chatbot! We'll start with a simple function that processes user input and returns appropriate responses.</p>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">Simple Chatbot Function</span>
                        <div class="code-actions">
                            <button class="code-btn copy-btn">Copy</button>
                            <button class="code-btn run-btn">Run</button>
                        </div>
                    </div>
                    <pre><code class="language-python">def simple_chatbot(user_input):
    """
    A simple rule-based chatbot that responds to user input
    """
    # Convert input to lowercase for easier matching
    user_input = user_input.lower().strip()
    
    # Greeting responses
    if any(word in user_input for word in ["hello", "hi", "hey"]):
        return "Hello! I'm your AI assistant. How can I help you today?"
    
    # Farewell responses
    elif any(word in user_input for word in ["bye", "goodbye", "see you"]):
        return "Goodbye! Thanks for chatting with me. Have a great day!"
    
    # Help responses
    elif "help" in user_input:
        return "I'm here to help! You can ask me about AI, programming, or just chat."
    
    # Name inquiry
    elif "name" in user_input:
        return "I'm ChatBot v1.0, your friendly AI learning companion!"
    
    # How are you
    elif any(phrase in user_input for phrase in ["how are you", "how do you do"]):
        return "I'm doing great! Thanks for asking. How are you doing?"
    
    # Default response
    else:
        return "That's interesting! Could you tell me more, or ask me something else?"

# Test the chatbot
test_inputs = ["Hello", "What's your name?", "Can you help me?", "Goodbye"]

for test_input in test_inputs:
    response = simple_chatbot(test_input)
    print(f"User: {test_input}")
    print(f"Bot: {response}")
    print("-" * 40)</code></pre>
                </div>

                <h3>Understanding the Code</h3>
                <ul>
                    <li><strong>Input Processing:</strong> We convert input to lowercase and remove extra spaces</li>
                    <li><strong>Keyword Matching:</strong> We use the <code>any()</code> function to check multiple keywords</li>
                    <li><strong>Fallback Response:</strong> The <code>else</code> clause handles unexpected inputs</li>
                    <li><strong>Modular Design:</strong> The function can be easily extended with more rules</li>
                </ul>
            </section>

            <!-- Step 4: Adding More Responses -->
            <section class="content-section" id="step4" style="display: none;">
                <div class="section-header">
                    <div class="section-number">4</div>
                    <h2 class="section-title-main">Adding More Responses</h2>
                </div>

                <p>Let's enhance our chatbot with more sophisticated responses and conversation patterns.</p>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">Enhanced Chatbot</span>
                        <div class="code-actions">
                            <button class="code-btn copy-btn">Copy</button>
                            <button class="code-btn run-btn">Run</button>
                        </div>
                    </div>
                    <pre><code class="language-python">import random

def enhanced_chatbot(user_input):
    """
    Enhanced chatbot with multiple response options and better pattern matching
    """
    user_input = user_input.lower().strip()

    # Greeting responses with variety
    greetings = [
        "Hello! Great to see you here!",
        "Hi there! Ready to learn about AI?",
        "Hey! Welcome to our AI learning session!"
    ]

    if any(word in user_input for word in ["hello", "hi", "hey"]):
        return random.choice(greetings)

    # AI-related questions
    elif any(word in user_input for word in ["ai", "artificial intelligence"]):
        return "AI is fascinating! It's about creating machines that can think and learn like humans. What aspect interests you most?"

    # Learning questions
    elif any(word in user_input for word in ["learn", "study", "understand"]):
        return "Learning is awesome! I'm here to help you understand AI concepts step by step. What would you like to explore?"

    # Programming questions
    elif any(word in user_input for word in ["python", "programming", "code"]):
        return "Python is perfect for AI! It's readable, powerful, and has amazing libraries. Are you new to programming?"

    # Emotional responses
    elif any(word in user_input for word in ["confused", "difficult", "hard"]):
        return "Don't worry! Learning can be challenging, but you're doing great. Let's break it down into smaller steps."

    elif any(word in user_input for word in ["excited", "fun", "cool"]):
        return "That's the spirit! AI is incredibly exciting. Your enthusiasm will take you far!"

    # Questions about the bot
    elif "what can you do" in user_input:
        return "I can help you learn AI concepts, answer questions about programming, and guide you through experiments!"

    # Farewell
    elif any(word in user_input for word in ["bye", "goodbye", "see you"]):
        farewells = [
            "Goodbye! Keep exploring AI!",
            "See you later! Happy learning!",
            "Bye! Remember, every expert was once a beginner!"
        ]
        return random.choice(farewells)

    # Default responses with variety
    else:
        defaults = [
            "That's interesting! Tell me more about that.",
            "I'd love to help! Could you be more specific?",
            "Hmm, I'm not sure about that. Can you rephrase?",
            "That's a great point! What else would you like to know?"
        ]
        return random.choice(defaults)

# Interactive conversation loop
def chat_session():
    print("🤖 AI Learning Chatbot v2.0")
    print("Type 'quit' to end the conversation")
    print("-" * 40)

    while True:
        user_input = input("You: ")

        if user_input.lower() in ['quit', 'exit', 'stop']:
            print("Bot: Thanks for chatting! Keep learning! 🚀")
            break

        response = enhanced_chatbot(user_input)
        print(f"Bot: {response}")
        print()

# Uncomment the line below to start chatting!
# chat_session()</code></pre>
                </div>

                <h3>New Features Added</h3>
                <ul>
                    <li><strong>Random Responses:</strong> Multiple options for more natural conversation</li>
                    <li><strong>Topic-Specific Responses:</strong> AI, programming, and learning topics</li>
                    <li><strong>Emotional Intelligence:</strong> Responds to user emotions</li>
                    <li><strong>Interactive Loop:</strong> Continuous conversation capability</li>
                </ul>
            </section>

            <!-- Step 5: Interactive Demo -->
            <section class="content-section" id="step5" style="display: none;">
                <div class="section-header">
                    <div class="section-number">5</div>
                    <h2 class="section-title-main">Interactive Demo</h2>
                </div>

                <p>Try out the chatbot yourself! Type different messages and see how it responds.</p>

                <div class="interactive-demo" data-demo-type="chatbot">
                    <h4 class="demo-title">🤖 Chatbot Simulator</h4>
                    <div class="demo-controls">
                        <input type="text" class="demo-input" placeholder="Type your message here..." style="flex: 1;">
                        <button class="demo-button">Send Message</button>
                    </div>
                    <div class="demo-output">Welcome! I'm your AI learning assistant. Try saying hello!</div>
                </div>

                <h3>Try These Sample Inputs</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0;">
                    <button class="demo-button" onclick="document.querySelector('.demo-input').value='Hello there!'">Hello there!</button>
                    <button class="demo-button" onclick="document.querySelector('.demo-input').value='What is AI?'">What is AI?</button>
                    <button class="demo-button" onclick="document.querySelector('.demo-input').value='I want to learn Python'">I want to learn Python</button>
                    <button class="demo-button" onclick="document.querySelector('.demo-input').value='This is confusing'">This is confusing</button>
                    <button class="demo-button" onclick="document.querySelector('.demo-input').value='What can you do?'">What can you do?</button>
                    <button class="demo-button" onclick="document.querySelector('.demo-input').value='Goodbye!'">Goodbye!</button>
                </div>
            </section>

            <!-- Step 6: Practice Exercises -->
            <section class="content-section" id="step6" style="display: none;">
                <div class="section-header">
                    <div class="section-number">6</div>
                    <h2 class="section-title-main">Practice Exercises</h2>
                </div>

                <div class="exercise-box">
                    <h4 class="exercise-title">🎯 Exercise 1: Extend the Chatbot</h4>
                    <div class="exercise-content">
                        <p><strong>Task:</strong> Add responses for these new topics:</p>
                        <ul>
                            <li>Weather inquiries ("weather", "temperature")</li>
                            <li>Time-related questions ("time", "date")</li>
                            <li>Math problems ("calculate", "math")</li>
                        </ul>
                        <p><strong>Bonus:</strong> Make the bot remember the user's name if they introduce themselves!</p>
                        <button class="demo-button complete-exercise">Mark Complete</button>
                    </div>
                </div>

                <div class="exercise-box">
                    <h4 class="exercise-title">🎯 Exercise 2: Conversation Context</h4>
                    <div class="exercise-content">
                        <p><strong>Challenge:</strong> Modify the chatbot to remember previous messages in the conversation. For example:</p>
                        <ul>
                            <li>If user says "I'm sad", bot responds with comfort</li>
                            <li>If user then says "thanks", bot knows to say "You're welcome! Feeling better?"</li>
                        </ul>
                        <p><strong>Hint:</strong> Use a list to store conversation history!</p>
                        <button class="demo-button complete-exercise">Mark Complete</button>
                    </div>
                </div>

                <div class="exercise-box">
                    <h4 class="exercise-title">🎯 Exercise 3: Smart Responses</h4>
                    <div class="exercise-content">
                        <p><strong>Advanced:</strong> Create a chatbot that can:</p>
                        <ul>
                            <li>Count words in user input and comment on message length</li>
                            <li>Detect questions (sentences ending with "?") and respond appropriately</li>
                            <li>Handle typos by suggesting corrections</li>
                        </ul>
                        <button class="demo-button complete-exercise">Mark Complete</button>
                    </div>
                </div>

                <h3>Key Takeaways</h3>
                <ul>
                    <li>Chatbots use conditional logic to process and respond to user input</li>
                    <li>String matching and keyword detection are fundamental techniques</li>
                    <li>Adding randomness makes conversations feel more natural</li>
                    <li>Good chatbots handle edge cases and unexpected inputs gracefully</li>
                    <li>This foundation scales to more advanced AI techniques like machine learning</li>
                </ul>

                <div style="background: var(--bg-secondary); padding: 2rem; border-radius: var(--border-radius-lg); margin: 2rem 0;">
                    <h4 style="color: var(--primary-color); margin-bottom: 1rem;">🚀 What's Next?</h4>
                    <p>You've built your first AI system! In the next experiment, we'll explore real-world AI applications and see how the concepts you learned here apply to healthcare, transportation, and environmental protection.</p>
                </div>
            </section>

            <!-- Navigation Buttons -->
            <div class="nav-buttons">
                <a href="#" class="nav-btn prev disabled">← Previous Step</a>
                <button class="nav-btn complete-experiment">Complete Experiment</button>
                <a href="#" class="nav-btn next">Next Step →</a>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="../../js/experiment.js"></script>
</body>
</html>
